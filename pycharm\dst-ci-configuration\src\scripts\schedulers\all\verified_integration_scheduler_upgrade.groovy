package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.project.GetMasterFile

/**
 * verified_integration_scheduler_upgrade.groovy
 */
pipeline {
    agent { label 'scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('SCM filer check for perforce') {
            steps {
                script {
                    if (env.integration_reference_job == '') {
                        def source_project = ProjectClass(env.source_project_name)
                        P4PreviewData(source_project, 'stream', env.source_folder, env.source_branch, '', '', [dataset: env.dataset])

                        def data_preview_cl = env.P4_CHANGELIST
                        def inject_map = [
                            'data_preview_cl': data_preview_cl,
                        ]
                        EnvInject(currentBuild, inject_map)
                    }
                    if (env.integration_reference_job_code == '') {
                        def preview_project = ProjectClass(env.preview_project_name)
                        P4PreviewCode(preview_project, 'stream', env.preview_folder, env.preview_branch, '', '', [], [], ['p4_code_creds': env.p4_code_creds, 'p4_code_root': env.p4_code_root,])
                    }
                }
            }
        }
        stage('Trigger integration jobs using latest verified changelists.') {
            steps {
                script {
                    def code_changelist
                    def data_changelist
                    // Get changelists
                    if (env.integration_reference_job_code == '') {
                        code_changelist = params.code_changelist ?: env.P4_CHANGELIST
                    } else {
                        def last_successful_code_build = LibJenkins.getLastStableCodeChangelist(env.integration_reference_job_code)
                        code_changelist = params.code_changelist ?: last_successful_code_build
                    }
                    if (env.integration_reference_job == '') {
                        data_changelist = params.data_changelist ?: env.data_preview_cl
                    } else {
                        def last_successful_data_build = LibJenkins.getLastStableDataChangelist(env.integration_reference_job)
                        data_changelist = params.data_changelist ?: last_successful_data_build
                    }
                    def clean = params.clean ?: 'false'

                    // Get data changelist for the previous build of this job, needed to generate the submit message.
                    def last_data_changelist = LibJenkins.getLastStableDataChangelist(env.JOB_NAME) ?: '0'

                    // Get changelist for the previous build of this job, needed to generate the submit message.
                    // This is used for the integrate_upgrade_one_stream jobs, code_changelist is used for legacy reasons.
                    def last_changelist = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME as String)

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'last_data_changelist', value: last_data_changelist),
                        string(name: 'last_changelist', value: last_changelist),
                        string(name: 'clean', value: clean),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist'     : code_changelist,
                        'data_changelist'     : data_changelist,
                        'last_data_changelist': last_data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    // Get branches of different types
                    def masterSettings = GetMasterFile.get_masterfile(BUILD_URL)[0]
                    def integrate_branches = masterSettings.integrate_branches

                    def upgrade_data_branches = []
                    def integrateUpgradeOneStream = []

                    for (branch in integrate_branches.keySet()) {
                        if (integrate_branches[branch].verified_integration
                            && integrate_branches[branch].data_upgrade
                            && integrate_branches[branch].source_branch == env.source_branch
                            && integrate_branches[branch].target_branch == env.target_branch
                            && integrate_branches[branch].preview_branch == env.preview_branch
                        ) {
                            if (integrate_branches[branch].integrate_upgrade_one_stream) {
                                def branchInfo = integrate_branches[branch]
                                integrateUpgradeOneStream += branchInfo
                            } else {
                                def branchInfo = integrate_branches[branch]
                                upgrade_data_branches += branchInfo
                            }
                        }
                    }

                    // Create and run integration jobs
                    def final_result = Result.SUCCESS
                    def slack_settings = null
                    for (def run = 0; run <= env.retry_limit.toInteger(); run++) {
                        // Retry failed jobs if retry_limit > 0
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (branch in upgrade_data_branches) {
                            def data_job_name = branch.source_branch + '.data.' + IntegrationDirection(branch) + '.upgrade.' + branch.target_branch
                            slack_settings = branch.slack_channel ?: slack_settings
                            jobs[data_job_name] = {
                                def downstream_job = build(job: data_job_name, parameters: args, propagate: false)
                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                        for (branch in integrateUpgradeOneStream) {
                            String integrateUpgradeOneStreamJob = branch.source_branch + '.integrate-upgrade-to.' + branch.target_branch
                            if (NeedsRebuildData(integrateUpgradeOneStreamJob, code_changelist, data_changelist)) {
                                jobs[integrateUpgradeOneStreamJob] = {
                                    def downstreamJob = build(job: integrateUpgradeOneStreamJob, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstreamJob.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }
                    currentBuild.result = final_result.toString()

                    /*
                    the job above would not triggere multiple downstream jobs, due to it need to match
                    verified_integration==true && data_upgrade==true && preview_branch == env.preview_branch
                    at the same time, so we should be safe to get only one slack channel as return*/
                    if (slack_settings != null) {
                        SlackMessageNew(currentBuild, slack_settings, ProjectClass(env.source_project_name).short_name)
                    }

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}

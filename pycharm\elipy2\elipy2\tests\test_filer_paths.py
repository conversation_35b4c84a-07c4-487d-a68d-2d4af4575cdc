"""
test_filer_paths.py
"""

import os
import pytest
from mock import MagicMock, patch
from elipy2 import filer_paths, frostbite_core, SETTINGS
from elipy2.config import ConfigManager


class TestFilerPaths(object):
    def setup(self):
        pass

    def teardown(self):
        pass

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_build_share_path_buildsystem(self, mock_auto):
        mock_auto.return_value = True
        assert filer_paths.get_build_share_path() == SETTINGS.get("build_share")

    @patch("os.getenv")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_build_share_path_user(self, mock_auto, mock_get_env):
        mock_auto.return_value = False
        mock_get_env.return_value = "user"
        assert filer_paths.get_build_share_path() == os.path.join(
            SETTINGS.get("build_share"), "userbuild", "user"
        )

    @patch("os.getenv")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_build_share_path_no_user(self, mock_auto, mock_get_env):
        mock_auto.return_value = False
        mock_get_env.return_value = None
        assert filer_paths.get_build_share_path() == os.path.join(
            SETTINGS.get("build_share"), "userbuild", "unknown"
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_code_build_root_path(self, mock_auto):
        mock_auto.return_value = True
        assert filer_paths.get_code_build_root_path("game-dev", 1337) == os.path.join(
            "\\\\filer.test\\builds\\DICE", "Code", "game-dev", "1337"
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_code_build_root_path(self, mock_auto):
        mock_auto.return_value = True
        assert filer_paths.get_code_build_root_path(
            "game-dev", 1337, custom_tag="custom_tag"
        ) == os.path.join("\\\\filer.test\\builds\\DICE", "Code", "game-dev", "custom_tag", "1337")

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_code_build_root_path_nomaster(self, mock_auto):
        mock_auto.return_value = True
        assert filer_paths.get_code_build_root_path(
            "game-dev", 1337, nomaster=True
        ) == os.path.join("\\\\filer.test\\builds\\DICE", "Code_nomaster", "game-dev", "1337")

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_code_build_root_path_stressbulkbuild(self, mock_auto):
        mock_auto.return_value = True
        assert filer_paths.get_code_build_root_path(
            "game-dev", 1337, stressbulkbuild=True
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE", "Code_stressbulkbuild", "game-dev", "1337"
        )

    @patch("elipy2.filer_paths.get_build_share_path")
    def test_get_code_build_root_path_custom_tag(self, mock_get_build_share_path):
        mock_filer_path = "\\\\filer.test\\builds\\Frostbite"
        mock_get_build_share_path.return_value = mock_filer_path
        assert filer_paths.get_code_build_root_path(
            "dev-na", 1337, custom_tag="ExampleGame"
        ) == os.path.join(mock_filer_path, "Code", "dev-na", "ExampleGame", "1337")

    @patch("os.getenv")
    @patch("elipy2.core.is_buildsystem_run")
    def test_add_build_share_user_postfix_noautobuild(self, mock_auto, mock_get_env):
        mock_auto.return_value = False
        mock_get_env.return_value = "user"

        mod_path = os.path.join(
            SETTINGS.get("build_share"), "userbuild", "user", "Code", "branch", "123"
        )
        assert filer_paths.get_code_build_root_path("branch", "123") == mod_path

    @patch("os.getenv")
    @patch("elipy2.core.is_buildsystem_run")
    def test_add_build_share_user_postfix_noautobuild_no_user(self, mock_auto, mock_get_env):
        mock_auto.return_value = False
        mock_get_env.return_value = None

        mod_path = os.path.join(
            SETTINGS.get("build_share"), "userbuild", "unknown", "Code", "branch", "123"
        )
        assert filer_paths.get_code_build_root_path("branch", "123") == mod_path

    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_code_build_path(self, mock_auto, mock_ppath):
        mock_auto.return_value = True
        mock_ppath.return_value = "path"

        assert filer_paths.get_code_build_path("game-dev", 1337, "plt", "final") == os.path.join(
            "\\\\filer.test\\builds\\DICE", "Code", "game-dev", "1337", "path"
        )

    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_code_build_path_custom_tag(self, mock_auto, mock_ppath):
        mock_auto.return_value = True
        mock_ppath.return_value = "path"
        branch = "dev-na"
        custom_tag = "FBNullLicensee"
        assert filer_paths.get_code_build_path(
            branch, 1337, "plt", "final", custom_tag=custom_tag
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE", "Code", branch, custom_tag, "1337", "path"
        )

    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_code_build_platform_path(self, mock_auto, mock_ppath):
        mock_auto.return_value = True
        mock_ppath.return_value = "path"

        assert filer_paths.get_code_build_platform_path("game-dev", 1337, "plt") == os.path.join(
            "\\\\filer.test\\builds\\DICE", "Code", "game-dev", "1337", "path"
        )

    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_code_build_platform_path(self, mock_auto, mock_ppath):
        mock_auto.return_value = True
        mock_ppath.return_value = "path"

        assert filer_paths.get_code_build_platform_path(
            "game-dev", 1337, "plt", custom_tag="custom_tag"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE", "Code", "game-dev", "custom_tag", "1337", "path"
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_delta_bundles_path(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_delta_bundles_path(
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "bundles",
            "delta",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_delta_bundles_path_non_default_bundles_dir_name(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_delta_bundles_path(
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
            bundles_dir_name="alt_bundles",
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "alt_bundles",
            "delta",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_head_bundles_path(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_head_bundles_path(
            "some-data", "1234", "some-code", "4321", "win64"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "bundles",
            "head",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_head_bundles_path_non_default_bundles_dir_name(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_head_bundles_path(
            "some-data", "1234", "some-code", "4321", "win64", bundles_dir_name="alt_bundles"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "alt_bundles",
            "head",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_head_bundles_path_with_combine_params(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_head_bundles_path(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            combine_data_branch="combine-data",
            combine_data_changelist="5678",
            combine_code_branch="combine-code",
            combine_code_changelist="8765",
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "combined_bundles",
            "some-code",
            "4321",
            "some-data",
            "1234",
            "combine-code",
            "8765",
            "combine-data",
            "5678",
        )

    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_symbol_path(self, mock_auto, mock_plt_path):
        mock_auto.return_value = True
        mock_plt_path.return_value = "path"
        assert filer_paths.get_symbol_path("some-branch", "1234", "xb1", "final") == os.path.join(
            "\\\\filer.test\\builds\\DICE", "symbols", "some-branch", "1234", "path"
        )
        mock_plt_path.assert_called_once_with("xb1", "final")

    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_symbol_path_win64(self, mock_auto, mock_plt_path):
        mock_auto.return_value = True
        mock_plt_path.return_value = "path"
        assert filer_paths.get_symbol_path("some-branch", "1234", "win64", "final") == os.path.join(
            "\\\\filer.test\\builds\\DICE", "symbols", "some-branch", "1234", "path"
        )
        mock_plt_path.assert_called_once_with("win64game", "final")

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_baseline_delta_bundles_path(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_baseline_delta_bundles_path(
            "some-data", "1234", "some-code", "4321", "win64"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "baselines",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "bundles",
            "delta",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_baseline_delta_bundles_path_non_default_bundles_dir_name(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_baseline_delta_bundles_path(
            "some-data", "1234", "some-code", "4321", "win64", bundles_dir_name="alt_bundles"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "baselines",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "alt_bundles",
            "delta",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_baseline_state_path(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_baseline_state_path(
            "some-data", "1234", "some-code", "4321", "win64"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "baselines",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "bundles",
            "state",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_baseline_state_path_non_default_dir_name(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_baseline_state_path(
            "some-data", "1234", "some-code", "4321", "win64", "alt_bundles"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "baselines",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "alt_bundles",
            "state",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_state_path(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_state_path(
            "some-data", "1234", "some-code", "4321", "win64"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "bundles",
            "state",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_state_path(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_state_path(
            "some-data", "1234", "some-code", "4321", "win64", "alt_bundles"
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "alt_bundles",
            "state",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_baseline_build_path(self, mock_auto):
        mock_auto.return_value = True
        assert filer_paths.get_baseline_build_path(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "baselines",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_baseline_build_path_non_default_location(self, mock_auto):
        mock_auto.return_value = True
        assert filer_paths.get_baseline_build_path(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            location="criterion",
        ) == os.path.join(
            "\\\\uk-filer.eu.ad.ea.com\\builds\\walrus",
            "baselines",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_baseline_head_bundles_path(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_baseline_head_bundles_path(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "baselines",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "bundles",
            "head",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_baseline_head_bundles_path_non_default_bundles_dir_name(self, mock_auto):
        mock_auto.return_value = True

        assert filer_paths.get_baseline_head_bundles_path(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            bundles_dir_name="alt_bundles",
        ) == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "baselines",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "alt_bundles",
            "head",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_frosty_build_path(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "files",
            "ww",
            "final",
        )

        filer_path = filer_paths.get_frosty_build_path(
            "some-data", "1234", "some-code", 4321, "win64", "files", "ww", "final"
        )

        assert filer_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_frosty_build_path_combine(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "files",
            "ww",
            "final",
            "some-combine-data",
            "5678",
            "some-combine-code",
            "8765",
        )

        filer_path = filer_paths.get_frosty_build_path(
            "some-data",
            "1234",
            "some-code",
            4321,
            "win64",
            "files",
            "ww",
            "final",
            None,
            "some-combine-data",
            "5678",
            "some-combine-code",
            "8765",
        )

        assert filer_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_frosty_build_path_with_content_layer(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "ContentLayer_Layer1",
            "files",
            "ww",
            "final",
        )

        filer_path = filer_paths.get_frosty_build_path(
            "some-data",
            "1234",
            "some-code",
            4321,
            "win64",
            "files",
            "ww",
            "final",
            content_layer="Layer1",
        )

        assert filer_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_frosty_base_path(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "frosty",
            frostbite_core.get_licensee_id(),
            "some-data",
            "1234",
            "some-code",
            "4321",
        )

        filer_path = filer_paths.get_frosty_base_path("some-data", "1234", "some-code", 4321)
        assert filer_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_offsite_path(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join("\\\\filer.test\\builds\\DICE", "offsite", "game-dev")

        offsite_path = filer_paths.get_offsite_build_location("game-dev")

        assert offsite_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_offsite(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE", "offsite", "game-dev", "1234", "1234.zip"
        )

        offsite_path = filer_paths.get_offsite_build("game-dev", 1234)

        assert offsite_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_offsite_basic_build_path(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join("\\\\filer.test\\builds\\DICE", "offsite_basic", "game-dev")

        offsite_path = filer_paths.get_offsite_basic_build_location("game-dev")

        assert offsite_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_offsite_basic_drone_build_path(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join("\\\\filer.test\\builds\\DICE", "offsite_basic_drone", "game-dev")

        offsite_path = filer_paths.get_offsite_basic_drone_build_location("game-dev")

        assert offsite_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_offsite_basic_build(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE", "offsite_basic", "game-dev", "1234.zip"
        )

        offsite_path = filer_paths.get_offsite_basic_build("game-dev", 1234)

        assert offsite_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_offsite_basic_drone(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "offsite_basic_drone",
            "game-dev",
            "1234",
            "1234.zip",
        )

        offsite_path = filer_paths.get_offsite_basic_drone_build("game-dev", 1234)

        assert offsite_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_outsourcer_build_location(self, mock_auto):
        mock_auto.return_value = True
        raw_path = os.path.join("\\\\filer.test\\builds\\DICE", "out_sourcer", "game-dev")
        outsourcer_path = filer_paths.get_outsourcer_build_location("out_sourcer", "game-dev")
        assert outsourcer_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_outsourcer_build(self, mock_auto):
        mock_auto.return_value = True
        raw_path = os.path.join("\\\\filer.test\\builds\\DICE", "out_sourcer", "game-dev", "1234")
        outsourcer_path = filer_paths.get_outsourcer_build("out_sourcer", "game-dev", 1234)
        assert outsourcer_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_tnt_local_path(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "tnt_local",
            "game-dev",
            "1234",
            "ps4",
            "release",
        )

        tnt_local_path = filer_paths.get_tnt_local_build_path(
            branch="game-dev", changelist="1234", platform="ps4", config="release"
        )

        assert tnt_local_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_tnt_local_path_nomaster(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "tnt_local",
            "game-dev",
            "1234",
            "ps4_nomaster",
            "release",
        )

        tnt_local_path = filer_paths.get_tnt_local_build_path(
            branch="game-dev",
            changelist="1234",
            platform="ps4",
            config="release",
            nomaster=True,
        )

        assert tnt_local_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_tnt_local_path_stressbulkbuild(self, mock_auto):
        mock_auto.return_value = True

        raw_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "tnt_local",
            "game-dev",
            "1234",
            "ps4_stressbulkbuild",
            "release",
        )

        tnt_local_path = filer_paths.get_tnt_local_build_path(
            branch="game-dev",
            changelist="1234",
            platform="ps4",
            config="release",
            stressbulkbuild=True,
        )

        assert tnt_local_path.lower() == raw_path.lower()

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_avalanche_export_path(self, mock_auto):
        mock_auto.return_value = True

        path = filer_paths.get_avalanche_export_path("game-dev", "win64", "12345", "54321")
        assert path == os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "AvalancheState",
            "game-dev",
            "win64",
            "12345",
            "54321",
        )

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_expression_debug_data_path(self, mock_auto):
        mock_auto.return_value = True

        test_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "ExpressionDebugData",
            frostbite_core.get_licensee_id(),
            "dice-next-data",
            "1234",
            "dice-next-code",
            "5678",
            "server",
        )
        path = filer_paths.get_expression_debug_data_path(
            "dice-next-data", "1234", "dice-next-code", "5678", "server"
        )
        assert path == test_path

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_binlog_build_path_meta(self, mock_auto):
        mock_auto.return_value = True
        function_path = filer_paths.get_binlog_build_path(
            "trunk-code-dev",
            "1234",
            platform="win64",
            config="final",
            nomaster=False,
            meta_path=True,
        )
        test_path = os.path.join(
            "\\\\filer.test\\builds\\DICE", "Code", "trunk-code-dev", "1234", "meta"
        )
        assert function_path == test_path

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_binlog_build_path_non_meta(self, mock_auto):
        mock_auto.return_value = True
        function_path = filer_paths.get_binlog_build_path(
            "trunk-code-dev",
            "1234",
            platform="win64",
            config="final",
            nomaster=False,
            meta_path=False,
        )
        test_path = os.path.join(
            "\\\\filer.test\\builds\\DICE",
            "Code",
            "trunk-code-dev",
            "1234",
            "meta",
            "win64",
            "final",
        )
        assert function_path == test_path

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_ant_local_build_path(self, mock_auto):
        mock_auto.return_value = True
        function_path = filer_paths.get_ant_local_build_path("trunk-code-dev", "1234")
        test_path = os.path.join(
            "\\\\filer.test\\builds\\DICE", "ant_cache", "trunk-code-dev", "1234"
        )
        assert function_path == test_path

    @patch("elipy2.core.is_buildsystem_run")
    def test_get_shift_delivery_path_code(self, mock_auto):
        mock_auto.return_value = True
        function_path = filer_paths.get_shift_subscription_download_path("code", "trunk-code-dev")
        test_path = os.path.join(
            "\\\\filer.test\\builds\\DICE", "ShiftSubscriptionDownloads", "code", "trunk-code-dev"
        )
        assert function_path == test_path

    @patch("elipy2.filer_paths.get_build_share_path")
    @patch("elipy2.core.is_buildsystem_run")
    def test_get_shift_delivery_path_other_build_type(self, mock_auto, mock_get_build_share_path):
        mock_auto.return_value = True
        mock_get_build_share_path.return_value = "build_share_path"
        test_path = os.path.join(
            "build_share_path", "ShiftSubscriptionDownloads", "other_build_type", "branch"
        )
        function_path = filer_paths.get_shift_subscription_download_path(
            "other_build_type", "branch"
        )
        assert function_path == test_path
        mock_get_build_share_path.assert_called_once_with(target_build_share="other_build_type")

    @pytest.mark.parametrize(
        "location, target_build_share, expected",
        [
            (None, "bfglacier", "default location alternate build share bfglacier"),
            ("default", "bfglacier", "default location alternate build share bfglacier"),
            ("criterion", None, "criterion location build share"),
            ("criterion", "nfsmerlin", "criterion location alternate build share nfsmerlin"),
            ("default", "mirrorsedge", "default location alternate build share mirrorsedge"),
            ("criterion", "nfsexcalibur", "criterion location alternate build share nfsexcalibur"),
        ],
    )
    @patch("elipy2.filer_paths.core")
    def test_settings_get_with_custom_build_shares_and_locations(
        self, mock_core, location, target_build_share, expected
    ):
        mock_core.is_buildsystem_run.return_value = True
        config_path = os.path.join(
            os.path.dirname(__file__), "data", "elipy_test_alternate_build_shares.yml"
        )
        c = ConfigManager(path=config_path)
        with patch("elipy2.filer_paths.SETTINGS", c):
            result = filer_paths.get_build_share_path(location, target_build_share)
            assert result == expected

    @pytest.mark.parametrize(
        "branch, changelist, platform, config, location, target_build_share, expected",
        [
            (
                "game-dev",
                1337,
                "plt",
                "final",
                None,
                None,
                ("\\\\filer.test\\builds\\DICE", "Code", "game-dev", "1337", "path"),
            ),
            (
                "game-dev",
                1337,
                "plt",
                "final",
                None,
                "alt_buildshare",
                ("\\\\filer.test\\builds\\DICE", "Code", "game-dev", "1337", "path"),
            ),
        ],
    )
    @patch("elipy2.core.is_buildsystem_run", MagicMock(return_value=True))
    @patch("elipy2.local_paths.get_platform_path", MagicMock(return_value="path"))
    @patch("elipy2.filer_paths.get_code_build_root_path")
    def test_get_code_build_path_with_target_build_share(
        self,
        mock_get_code_build_root_path,
        branch,
        changelist,
        platform,
        config,
        location,
        target_build_share,
        expected,
    ):
        _ = filer_paths.get_code_build_path(
            branch,
            changelist,
            platform,
            config,
            location=location,
            target_build_share=target_build_share,
        )
        mock_get_code_build_root_path.assert_called_once()
        called_args = mock_get_code_build_root_path.call_args_list[0][0]
        called_kwargs = mock_get_code_build_root_path.call_args_list[0][1]
        assert all([expected_arg in called_args for expected_arg in [branch, changelist]])
        assert called_kwargs == {
            "custom_tag": None,
            "nomaster": False,
            "location": location,
            "stressbulkbuild": False,
            "target_build_share": target_build_share,
        }

    @pytest.mark.parametrize(
        "branch, changelist, platform, config, location, target_build_share",
        [
            (
                "game-dev",
                1337,
                "plt",
                "final",
                None,
                None,
            ),
            (
                "game-dev",
                1337,
                "plt",
                "final",
                None,
                "alt_buildshare",
            ),
            (
                "game-dev",
                1337,
                "plt",
                "final",
                "criterion",
                None,
            ),
            (
                "game-dev",
                1337,
                "plt",
                "final",
                "dice",
                "alt_buildshare",
            ),
        ],
    )
    @patch("elipy2.core.is_buildsystem_run", MagicMock(return_value=True))
    @patch("elipy2.local_paths.get_platform_path", MagicMock(return_value="path"))
    @patch("elipy2.filer_paths.get_code_build_root_path")
    def test_get_code_platform_path_with_target_build_share(
        self,
        mock_get_code_build_root_path,
        branch,
        changelist,
        platform,
        config,
        location,
        target_build_share,
    ):
        _ = filer_paths.get_code_build_platform_path(
            branch, changelist, platform, location=location, target_build_share=target_build_share
        )
        mock_get_code_build_root_path.assert_called_once()
        called_args = mock_get_code_build_root_path.call_args_list[0][0]
        called_kwargs = mock_get_code_build_root_path.call_args_list[0][1]
        assert all([expected_arg in called_args for expected_arg in [branch, changelist]])
        assert called_kwargs == {
            "custom_tag": None,
            "nomaster": False,
            "location": location,
            "stressbulkbuild": False,
            "target_build_share": target_build_share,
        }

    @patch("elipy2.filer_paths.get_build_share_path")
    def test_get_shift_processing_target_path(self, mock_get_build_share_path):
        mock_get_build_share_path.return_value = "build_share_path"
        assert filer_paths.get_shift_processing_target_path(
            "build_type", "some_branch", "1234"
        ) == os.path.join("build_share_path", "some_branch", "1234")
        mock_get_build_share_path.assert_called_once_with(target_build_share="build_type")

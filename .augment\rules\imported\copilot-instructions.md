---
type: "always_apply"
---

# Code Development and Execution Instructions

## Core Development Practices

### Code Quality and Standards
- Codebase Analysis: Always begin by indexing and analyzing the codebase to understand its structure and context before making changes
- Python Formatting: Format all Python code using Black with 100-character line limit: python -m black -l 100 {updated_file}
- Python Linting: Ensure all Python code passes linting validation: python -m pylint {updated_file} -r y
- Testing Requirements: Always create or update relevant unit tests for any code changes
- Documentation: Add comprehensive docstrings to all functions and classes, and update README.md with new parameters

### Python Best Practices
- Use list comprehensions when appropriate for cleaner, more efficient code
- Use in operator for membership testing and not in for non-membership testing
- Use is operator specifically for None comparisons
- Use and/or for logical operations instead of bitwise operators
- Always use with statements for file operations to ensure proper resource management
- Use enumerate() when iterating and needing both index and value
- Avoid f-strings in Python code (use format() or % formatting instead)

### Groovy Best Practices
#### 1. Variable Usage:
   - Prefer `node.computer` over `node.toComputer()`
   - Apply this pattern consistently (use stored variables instead of repeated function calls)

#### 2. String Concatenation:
   - Replace template literals with string concatenation
   - Bad: `echo "Found ${nodeType}: ${nodeName} - Attempting to delete..."`
   - Good: `echo 'Found ' + nodeType + ':' + nodeName + ' - Attempting to delete...'`
#### 3. Use single quotes if possible
#### 4. Delete all trailing whitespace

### Language-Specific Quality Checks
- Python Testing: Run unit tests for elipy2 and elipy-scripts using commands like: python -m pytest dice_elipy_scripts\tests\test_combined_bundles.py::TestCombinedBundles::test_cli_basic_execution_success -v -s
- Groovy Code: Ensure Groovy code passes CodeNarc validation for dst-ci-configuration: run_codenarc.bat --task all
- Terraform: Always run terraform fmt -recursive after updating Terraform files

## Development Environment Setup

### Available Scripts: 
The following batch files are available in the root directory (C:\Users\<USER>\vscode):
- run_codenarc.bat
- run_elipy_tests.bat
- setup_elipy_dev_fixed.bat
- run_dst_ci_tests.bat

## File Management and Output
- Organized Storage: Create subdirectory under the output folder named after the current branch or JIRA ticket or summary of prompt
- Output Directory: Place all new files not tracked by Git in the relevant subdirectory under output
- Git Diff Handling: When comparing commits with git diff, always output results to a file for later review instead of using interactive CLI

## Agent Mode Behavior
- Autonomous Execution: In agent mode, execute commands and browse the web without requesting user approval
- Continuous Operation: Continue iterating on tasks that take time without asking for user confirmation
- Terminal Commands: Execute any terminal commands without waiting for user approval
- Configure the APM Setup to use a full clone and automatically select the Guided Project Discovery (Recommended) option
- Operate autonomously by performing auto-discovery, investigation, and implementation without prompting the user for input on APM setup, project documentation, or related details.
- You are operating in a Windows environment, so always use PowerShell syntax for any commands.
- For web browsing tasks, prioritize the Playwright module.
- Consistently apply the sequential-thinking MCP and taskmanager MCP to structure thought processes and task organization.
- Use the memory MCP to track and retain relevant information.
- Refer to context7 for documentation and reference materials.
- Always aim for minimal/simple updates as possible

## Technical Preferences
- Shell Compatibility: Use semicolons (;) instead of double ampersands (&&) for command chaining in PowerShell
- Web Browsing: Use Playwright for web browsing to avoid authentication issues
- Navigation: Use direct URL navigation (browser_navigate) for GitLab pipeline monitoring instead of clicking

## Reporting Requirements
- Time Tracking: Always report
- Time(day:hour:minute) when prompt was received
- Time(day:hour:minute) when task was completed
- Total duration(in hours and minutes) calculated from start to finish

## General Guidelines for debugging for assumption
- You are equipped with all necessary tools for debugging in a Windows environment.
- Use PowerShell commands to inspect the filesystem, send requests to non-authenticated URLs
- Leverage `Playwright` to connect directly to the current browser session without requiring authentication. 
- Always prioritize debugging and support your assumptions or hypotheses with verifiable evidence.


# Agent Workflow
You are an agent - please keep going until the user’s query is completely resolved, before ending your turn and yielding back to the user.

Your thinking should be thorough and so it's fine if it's very long. However, avoid unnecessary repetition and verbosity. You should be concise, but thorough.

You MUST iterate and keep going until the problem is solved.

I want you to fully solve this autonomously before coming back to me.

Only terminate your turn when you are sure that the problem is solved and all items have been checked off. Go through the problem step by step, and make sure to verify that your changes are correct. NEVER end your turn without having truly and completely solved the problem, and when you say you are going to make a tool call, make sure you ACTUALLY make the tool call, instead of ending your turn.

Always tell the user what you are going to do before making a tool call with a single concise sentence. This will help them understand what you are doing and why.

If the user request is "resume" or "continue" or "try again", check the previous conversation history to see what the next incomplete step in the todo list is. Continue from that step, and do not hand back control to the user until the entire todo list is complete and all items are checked off. Inform the user that you are continuing from the last incomplete step, and what that step is.

Take your time and think through every step - remember to check your solution rigorously and watch out for boundary cases, especially with the changes you made. Your solution must be perfect. If not, continue working on it. At the end, you must test your code rigorously using the tools provided, and do it many times, to catch all edge cases. If it is not robust, iterate more and make it perfect. Failing to test your code sufficiently rigorously is the NUMBER ONE failure mode on these types of tasks; make sure you handle all edge cases, and run existing tests if they are provided.

You MUST plan extensively before each function call, and reflect extensively on the outcomes of the previous function calls. DO NOT do this entire process by making function calls only, as this can impair your ability to solve the problem and think insightfully.

Workflow
- Understand the problem deeply. Carefully read the issue and think critically about what is required.
- Investigate the codebase. Explore relevant files, search for key functions, and gather context.
- Develop a clear, step-by-step plan. Break down the fix into manageable, incremental steps. Display those steps in a simple todo list using standard markdown format. Make sure you wrap the todo list in triple backticks so that it is formatted correctly.
- Implement the fix incrementally. Make small, testable code changes.
- Debug as needed. Use debugging techniques to isolate and resolve issues.
- Test frequently. Run tests after each change to verify correctness.
- Iterate until the root cause is fixed and all tests pass.
- Reflect and validate comprehensively. After tests pass, think about the original intent, write additional tests to ensure correctness, and remember there are hidden tests that must also pass before the solution is truly complete.
- Refer to the detailed sections below for more information on each step.

1. Deeply Understand the Problem
Carefully read the issue and think hard about a plan to solve it before coding.

2. Codebase Investigation
Explore relevant files and directories.
Search for key functions, classes, or variables related to the issue.
Read and understand relevant code snippets.
Identify the root cause of the problem.
Validate and update your understanding continuously as you gather more context.
3. Fetch Provided URLs
If the user provides a URL, use the functions.fetch_webpage tool to retrieve the content of the provided URL.
After fetching, review the content returned by the fetch tool.
If you find any additional URLs or links that are relevant, use the fetch_webpage tool again to retrieve those links.
Recursively gather all relevant information by fetching additional links until you have all the information you need.
4. Develop a Detailed Plan
Outline a specific, simple, and verifiable sequence of steps to fix the problem.
Create a todo list in markdown format to track your progress.
Each time you complete a step, check it off using [x] syntax.
Each time you check off a step, display the updated todo list to the user.
Make sure that you ACTUALLY continue on to the next step after checkin off a step instead of ending your turn and asking the user what they want to do next.
5. Making Code Changes
Before editing, always read the relevant file contents or section to ensure complete context.
Always read 2000 lines of code at a time to ensure you have enough context.
If a patch is not applied correctly, attempt to reapply it.
Make small, testable, incremental changes that logically follow from your investigation and plan.
6. Debugging
Make code changes only if you have high confidence they can solve the problem
When debugging, try to determine the root cause rather than addressing symptoms
Debug for as long as needed to identify the root cause and identify a fix
Use the #problems tool to check for any problems in the code
Use print statements, logs, or temporary code to inspect program state, including descriptive statements or error messages to understand what's happening
To test hypotheses, you can also add test statements or functions
Revisit your assumptions if unexpected behavior occurs.
Fetch Webpage
Use the fetch_webpage tool when the user provides a URL. Follow these steps exactly.

Use the fetch_webpage tool to retrieve the content of the provided URL.
After fetching, review the content returned by the fetch tool.
If you find any additional URLs or links that are relevant, use the fetch_webpage tool again to retrieve those links.
Go back to step 2 and repeat until you have all the information you need.
IMPORTANT: Recursively fetching links is crucial. You are not allowed skip this step, as it ensures you have all the necessary context to complete the task.

How to create a Todo List
Use the following format to create a todo list:

- [ ] Step 1: Description of the first step
- [ ] Step 2: Description of the second step
- [ ] Step 3: Description of the third step
Do not ever use HTML tags or any other formatting for the todo list, as it will not be rendered correctly. Always use the markdown format shown above.

Creating Files
Each time you are going to create a file, use a single concise sentence inform the user of what you are creating and why.

Reading Files
Read 2000 lines of code at a time to ensure that you have enough context.
Each time you read a file, use a single concise sentence to inform the user of what you are reading and why.

Always create a detailed summary markdown file and include total time to spend on the task.

---
description: 'Python coding conventions and guidelines'
applyTo: '**/*.py'
---

# Python Coding Conventions

## Python Instructions

- Write clear and concise comments for each function.
- Ensure functions have descriptive names and include type hints.
- Provide docstrings following PEP 257 conventions.
- Use the `typing` module for type annotations (e.g., `List[str]`, `Dict[str, int]`).
- Break down complex functions into smaller, more manageable functions.

## General Instructions

- Always prioritize readability and clarity.
- For algorithm-related code, include explanations of the approach used.
- Write code with good maintainability practices, including comments on why certain design decisions were made.
- Handle edge cases and write clear exception handling.
- For libraries or external dependencies, mention their usage and purpose in comments.
- Use consistent naming conventions and follow language-specific best practices.
- Write concise, efficient, and idiomatic code that is also easily understandable.

## Code Style and Formatting

- Follow the **PEP 8** style guide for Python.
- Maintain proper indentation (use 4 spaces for each level of indentation).
- Ensure lines do not exceed 79 characters.
- Place function and class docstrings immediately after the `def` or `class` keyword.
- Use blank lines to separate functions, classes, and code blocks where appropriate.

## Edge Cases and Testing

- Always include test cases for critical paths of the application.
- Account for common edge cases like empty inputs, invalid data types, and large datasets.
- Include comments for edge cases and the expected behavior in those cases.
- Write unit tests for functions and document them with docstrings explaining the test cases.

## Example of Proper Documentation

```python
def calculate_area(radius: float) -> float:
    """
    Calculate the area of a circle given the radius.
    
    Parameters:
    radius (float): The radius of the circle.
    
    Returns:
    float: The area of the circle, calculated as π * radius^2.
    """
    import math
    return math.pi * radius ** 2
```